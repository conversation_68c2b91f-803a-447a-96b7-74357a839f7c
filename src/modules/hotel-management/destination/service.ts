import { MedusaService } from "@camped-ai/framework/utils";
import { Destination } from "./models/destination";
import { DestinationImage } from "./models/destination-image";
import { DestinationFaq } from "./models/destination-faq";
import { uploadFilesWorkflow } from "@camped-ai/medusa/core-flows";

class DestinationModuleService extends MedusaService({
  Destination,
  DestinationImage,
  DestinationFaq,
}) {
  async uploadImages(destinationId, files, container) {
    const { result } = await uploadFilesWorkflow(container).run({
      input: {
        files: files.map((f) => ({
          filename: f.originalname,
          mimeType: f.mimetype,
          content: f.buffer.toString("binary"),
          access: "public",
        })),
      },
    });

    const destinationImages = [];
    for (const file of result) {
      const destinationImage = await this.createDestinationImage({
        destination_id: destinationId,
        url: file.url,
        metadata: {
          fileKey: (file as any).key,
          size: (file as any).size,
          mimeType: (file as any).mimeType,
        },
      });
      destinationImages.push(destinationImage);
    }

    return destinationImages;
  }

  async createDestinationImage(data) {
    const existingImages = await this.listDestinationImages({
      destination_id: data.destination_id,
    }, {
      order: { rank: "DESC" },
      take: 1
    });

    const nextRank = existingImages.length ? existingImages[0].rank + 1 : 0;

    return await this.createDestinationImages({
      url: data.url,
      metadata: data.metadata,
      rank: nextRank,
      destination_id: data.destination_id
    });
  }

  async updateDestinationImageRank(imageId, rank) {
    const image = await this.retrieveDestinationImage(imageId);

    if (!image) {
      throw new Error(`Image with id ${imageId} not found`);
    }

    return await this.updateDestinationImages({
      id: imageId,
      rank: rank
    });
  }

  async setDestinationThumbnail(destinationId, imageId) {
    const image = await this.retrieveDestinationImage(imageId);

    if (!image) {
      throw new Error(`Image with id ${imageId} not found`);
    }

    // First, get all images for this destination
    const allImages = await this.listDestinationImages({
      destination_id: destinationId
    });

    // Update all images to set isThumbnail: false
    for (const img of allImages) {
      if (img.metadata?.isThumbnail) {
        await this.updateDestinationImages({
          id: img.id,
          metadata: {
            ...img.metadata,
            isThumbnail: false
          }
        });
      }
    }

    // Set the selected image as thumbnail without changing its rank
    // This preserves the current order of images
    return await this.updateDestinationImages({
      id: imageId,
      metadata: {
        ...image.metadata,
        isThumbnail: true
      }
    });
  }

  async getDestinationImages(destinationId) {
    const images = await this.listDestinationImages({
      destination_id: destinationId
    }, {
      order: { rank: "ASC" }
    });

    return images.map(image => ({
      id: image.id,
      url: image.url,
      isThumbnail: image.metadata?.isThumbnail || false,
      rank: image.rank
    }));
  }

  async deleteDestinationImage(imageId) {
    const image = await this.retrieveDestinationImage(imageId);

    if (!image) {
      throw new Error(`Image with id ${imageId} not found`);
    }

    await this.deleteDestinationImages({
      id: imageId
    });

    return { id: imageId, deleted: true };
  }

  // FAQ Methods
  async createDestinationFaq(data) {
    // Ensure we don't pass any ID to avoid conflicts
    const faqData = {
      question: data.question,
      answer: data.answer,
      destination_id: data.destination_id
    };

    return await this.createDestinationFaqs(faqData);
  }

  async getDestinationFaqs(destinationId) {
    return await this.listDestinationFaqs({
      destination_id: destinationId
    });
  }

  async updateDestinationFaq(faqId, data) {
    console.log(`Updating FAQ ${faqId} with data:`, data);

    const faq = await this.retrieveDestinationFaq(faqId);

    if (!faq) {
      throw new Error(`FAQ with id ${faqId} not found`);
    }

    console.log('Current FAQ before update:', faq);

    // Use the same pattern as image updates
    const result = await this.updateDestinationFaqs({
      id: faqId,
      question: data.question,
      answer: data.answer
    });

    console.log('FAQ update result:', result);
    return result;
  }

  async deleteDestinationFaq(faqId) {
    const faq = await this.retrieveDestinationFaq(faqId);

    if (!faq) {
      throw new Error(`FAQ with id ${faqId} not found`);
    }

    await this.deleteDestinationFaqs({
      id: faqId
    });

    return { id: faqId, deleted: true };
  }
}

export default DestinationModuleService;
