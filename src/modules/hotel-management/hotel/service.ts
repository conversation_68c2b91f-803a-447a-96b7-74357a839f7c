import { MedusaService } from "@camped-ai/framework/utils";
import { Hotel } from "./models/hotel";
import { HotelImage } from "./models/hotel-image";
import { uploadFilesWorkflow } from "@camped-ai/medusa/core-flows";
import { RoomConfig } from "./models/room-config";

class HotelModuleService extends MedusaService({
  Hotel,
  HotelImage,
  RoomConfig
}) {
  // Upload images and create HotelImage records
  async uploadImages(hotelId, files, container) {
    // First, upload the files using the file module
    const { result } = await uploadFilesWorkflow(container).run({
      input: {
        files: files.map((f) => ({
          filename: f.originalname,
          mimeType: f.mimetype,
          content: f.buffer.toString("binary"),
          access: "public",
        })),
      },
    });

    // Then create HotelImage records for each uploaded file
    const hotelImages = [];
    for (const file of result) {
      const hotelImage = await this.createHotelImage({
        hotel_id: hotelId,
        url: file.url,
        metadata: {
          fileKey: file.key,
          size: file.size,
          mimeType: file.mimeType,
        },
      });
      hotelImages.push(hotelImage);
    }

    return hotelImages;
  }

  // Create a single HotelImage record
  async createHotelImage(data) {
    // Get the current highest rank for this hotel's images
    const existingImages = await this.listHotelImages({
      hotel_id: data.hotel_id,
    }, {
      order: { rank: "DESC" },
      take: 1
    });

    const nextRank = existingImages.length ? existingImages[0].rank + 1 : 0;

    // Create the hotel image
    return await this.createHotelImages({
      url: data.url,
      metadata: data.metadata,
      rank: nextRank,
      hotel_id: data.hotel_id
    });
  }

  // Add these methods to your HotelModuleService
async updateHotelImageRank(imageId, rank) {
  const image = await this.retrieveHotelImage(imageId);

  if (!image) {
    throw new Error(`Image with id ${imageId} not found`);
  }

  // Update the image rank
  const updatedImage = await this.updateHotelImages({
    id: imageId,
    rank: rank
  });

  return updatedImage;
}

async setHotelThumbnail(hotelId, imageId) {
  console.log({hotelId,imageId})
  const image = await this.retrieveHotelImage(imageId);

  if (!image) {
    throw new Error(`Image with id ${imageId} not found`);
  }

  // First, update all images for this hotel to not be thumbnails
  const allImages = await this.listHotelImages({
    hotel_id: hotelId
  });

  // Update all images to not be thumbnails
  for (const img of allImages) {
    await this.updateHotelImages({
      id: img.id,
      metadata: {
        ...img.metadata,
        isThumbnail: false
      }
    });
  }

  // Then set the selected image as thumbnail without changing its rank
  // This preserves the current order of images
  const updatedImage = await this.updateHotelImages({
    id: image.id,
    metadata: {
      ...image.metadata,
      isThumbnail: true
    }
  });

  return updatedImage;
}

  // Retrieve images for a specific hotel
  async getHotelImages(hotelId) {
    const images = await this.listHotelImages({
      hotel_id: hotelId
    }, {
      order: { rank: "ASC" } // Order by rank to ensure consistent order
    });

    return images.map(image => ({
      id: image.id,
      url: image.url,
      isThumbnail: image.metadata?.isThumbnail || false,
      rank: image.rank
    }));
  }

  // Add this method to your HotelModuleService
async deleteHotelImage(imageId) {
  // Find the image first to make sure it exists
  const image = await this.retrieveHotelImage(imageId);

  if (!image) {
    throw new Error(`Image with id ${imageId} not found`);
  }

  // Delete the image record
  await this.deleteHotelImages({
    id: imageId
  });

  return { id: imageId, deleted: true };
}
}

export default HotelModuleService;